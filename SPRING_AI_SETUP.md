# Configuração do Spring AI com Gemini

Este documento descreve como configurar e usar o assistente de IA integrado ao sistema de estudos organizados.

## Pré-requisitos

1. **Google Cloud Project**: Você precisa ter um projeto no Google Cloud Platform
2. **Vertex AI API**: Habilitar a API do Vertex AI no seu projeto
3. **Autenticação**: Configurar credenciais do Google Cloud

## Configuração

### 1. Configurar Google Cloud

```bash
# Instalar Google Cloud CLI
# https://cloud.google.com/sdk/docs/install

# Fazer login
gcloud auth login

# Configurar projeto
gcloud config set project YOUR_PROJECT_ID

# Configurar credenciais padrão da aplicação
gcloud auth application-default login
```

### 2. Configurar application.properties

Edite o arquivo `src/main/resources/application.properties`:

```properties
# Spring AI - Vertex AI Gemini Configuration
spring.ai.vertex.ai.project-id=your-google-cloud-project-id
spring.ai.vertex.ai.location=us-central1
spring.ai.vertex.ai.gemini.model=gemini-1.5-pro
spring.ai.vertex.ai.gemini.temperature=0.7
spring.ai.vertex.ai.gemini.max-output-tokens=2048
```

### 3. Variáveis de Ambiente (Alternativa)

Como alternativa, você pode usar variáveis de ambiente:

```bash
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
export SPRING_AI_VERTEX_AI_PROJECT_ID=your-project-id
```

## Funcionalidades do Assistente

O assistente pode executar as seguintes operações:

### 1. Cadastrar Disciplina
```json
{
  "mensagem": "Cadastre a disciplina 'Matemática Financeira'"
}
```

### 2. Criar Planejamento com IA
```json
{
  "mensagem": "Quero criar um planejamento de estudos para concurso público. Tenho 20 horas semanais para estudar Português, Matemática e Direito Administrativo. Meu nível em Português é 4, Matemática é 2 e Direito é 3. O peso das matérias é 5, 4 e 5 respectivamente."
}
```

### 3. Conversação Geral
```json
{
  "mensagem": "Como você pode me ajudar?"
}
```

## Endpoint da API

### POST /v1/assistant/chat

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "mensagem": "Sua mensagem aqui"
}
```

**Response:**
```json
{
  "resposta": "Resposta do assistente"
}
```

## Exemplos de Uso

### Exemplo 1: Cadastrar Disciplina
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Cadastre a disciplina Física Quântica"}'
```

### Exemplo 2: Criar Planejamento
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Crie um planejamento de 15 horas semanais para Matemática e Português"}'
```

## Troubleshooting

### Erro de Autenticação
- Verifique se executou `gcloud auth application-default login`
- Confirme se o project-id está correto
- Verifique se a API do Vertex AI está habilitada

### Erro de Quota
- Verifique os limites da API no Google Cloud Console
- Considere aumentar as quotas se necessário

### Erro de Modelo
- Confirme se o modelo `gemini-1.5-pro` está disponível na sua região
- Tente usar `gemini-1.0-pro` como alternativa

## Logs

Para debug, habilite logs detalhados:

```properties
logging.level.org.springframework.ai=DEBUG
logging.level.br.com.estudoorganizado.controller.AssistantController=DEBUG
```
