# JWT Configuration
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Spring AI - Vertex AI Gemini Configuration
# Substitua pelos seus valores reais do Google Cloud
spring.ai.vertex.ai.gemini.project-id=your-google-cloud-project-id
spring.ai.vertex.ai.gemini.location=us-central1
spring.ai.vertex.ai.gemini.model=gemini-1.5-pro
spring.ai.vertex.ai.gemini.temperature=0.7
spring.ai.vertex.ai.gemini.max-output-tokens=2048

# Configuracao de credenciais do Google Cloud
# Defina a variavel de ambiente GOOGLE_APPLICATION_CREDENTIALS
# export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"

# Para desabilitar o Spring AI (caso nao queira usar), comente as linhas acima
# ou remova a propriedade spring.ai.vertex.ai.gemini.project-id
