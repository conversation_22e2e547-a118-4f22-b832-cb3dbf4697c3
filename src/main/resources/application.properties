# JWT Configuration
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=admin
spring.datasource.password=123
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Spring AI - Vertex AI Gemini Configuration
# gemini-1.0-pro
# gemini-1.5-pro-latest
# gemini-1.5-flash-latest
#spring.ai.vertex.ai.gemini.project-id=estudo-organizado-gemini-ai
#spring.ai.vertex.ai.gemini.location=europe-west1
#spring.ai.vertex.ai.gemini.chat.options.model=gemini-1.5-pro-latest
#spring.ai.vertex.ai.gemini.chat.options.temperature=0.5
#spring.ai.vertex.ai.gemini.chat.options.max-output-tokens=2048

# Configuracao de credenciais do Google Cloud (usar variavel de ambiente e mais seguro)
# GOOGLE_APPLICATION_CREDENTIALS deve apontar para o arquivo JSON das credenciais