# JWT Configuration
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=admin
spring.datasource.password=123
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Spring AI - Vertex AI Gemini Configuration (OPCIONAL)
spring.ai.vertex.ai.gemini.project-id=project_id
spring.ai.vertex.ai.api-key=api_key
spring.ai.vertex.ai.gemini.location=us-central1