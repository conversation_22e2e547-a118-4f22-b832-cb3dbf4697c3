package br.com.estudoorganizado.controller;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.vertexai.VertexAI;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/v1/vertex-ai")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "spring.ai.vertex.ai.project-id")
public class VertexAiTestController {

    @Autowired
    private VertexAI vertexAI;

    @Autowired
    private ChatClient chatClient;

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Verificar configuração do VertexAI
            response.put("projectId", vertexAI.getProjectId());
            response.put("location", vertexAI.getLocation());
            response.put("vertexAiConfigured", true);
            
            // Verificar credenciais
            GoogleCredentials credentials = GoogleCredentials.getApplicationDefault();
            response.put("credentialsFound", credentials != null);
            
            if (credentials.getAccessToken() != null) {
                response.put("accessTokenValid", true);
            } else {
                credentials.refresh();
                response.put("accessTokenValid", true);
                response.put("refreshed", true);
            }
            
            response.put("status", "OK");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Erro no health check do Vertex AI: {}", e.getMessage());
            response.put("status", "ERROR");
            response.put("error", e.getMessage());
            response.put("vertexAiConfigured", false);
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/test-simple")
    public ResponseEntity<Map<String, Object>> testSimpleChat() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Testando comunicação simples com Gemini...");
            
            String result = chatClient.prompt()
                .user("Diga apenas 'Olá' em português")
                .call()
                .content();
            
            response.put("status", "SUCCESS");
            response.put("response", result);
            response.put("message", "Comunicação com Gemini funcionando!");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Erro ao testar comunicação com Gemini: {}", e.getMessage(), e);
            response.put("status", "ERROR");
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(response);
        }
    }
}
