package br.com.estudoorganizado.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "spring.ai.vertex.ai.gemini.project-id")
public class ChatClientConfig {

    @Bean
    @ConditionalOnBean(ChatModel.class)
    public ChatClient chatClient(ChatModel chatModel) {
        // Log para debug
        System.out.println("Criando ChatClient com modelo: " + chatModel.getClass().getSimpleName());

        return ChatClient.builder(chatModel)
            .defaultSystem("Você é um assistente especializado em planejamento de estudos para concursos públicos. " +
                "Você pode ajudar os usuários a cadastrar disciplinas, criar e gerenciar planejamentos de estudo. " +
                "Seja sempre educado, claro e objetivo em suas respostas. " +
                "Quando o usuário solicitar uma ação específica, use as funções disponíveis para executá-la.")
            .build();
    }
}
