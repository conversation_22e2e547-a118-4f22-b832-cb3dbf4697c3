# Configuração do Vertex AI - Resolução do Erro "project-id must be set"

## Problema Identificado

O erro `Vertex AI project-id must be set!` ocorre quando o Spring AI não consegue ler corretamente as configurações do Vertex AI.

## Soluções Implementadas

### 1. Configuração Manual do VertexAI Bean

Criamos uma configuração manual em `VertexAiConfig.java` que:
- Define explicitamente o `project-id` e `location`
- Configura as credenciais automaticamente
- Cria um bean `VertexAI` customizado

### 2. Múltiplas Opções de Autenticação

A configuração suporta:

#### Opção A: Arquivo de Credenciais (Recomendado)
```bash
# Coloque o arquivo de credenciais na pasta do projeto
env-gemini/gen-lang-client-**********-e8c38b0d9293.json
```

#### Opção B: Credenciais Padrão do gcloud
```bash
gcloud auth application-default login
```

#### Opção C: Variável de Ambiente
```bash
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
```

### 3. Configurações no application.properties

```properties
# Vertex AI Configuration
spring.ai.vertex.ai.project-id=gen-lang-client-**********
spring.ai.vertex.ai.location=us-central1

# Gemini Model Configuration  
spring.ai.vertex.ai.gemini.model=gemini-1.5-flash
spring.ai.vertex.ai.gemini.temperature=0.7
spring.ai.vertex.ai.gemini.max-output-tokens=2048
```

## Como Testar

### 1. Verificar Logs na Inicialização

Ao iniciar a aplicação, você deve ver:
```
Configurado GOOGLE_APPLICATION_CREDENTIALS: /path/to/credentials.json
Project ID configurado: gen-lang-client-**********
Location configurado: us-central1
```

### 2. Testar o Endpoint

```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Olá, como você pode me ajudar?"}'
```

## Troubleshooting

### Se ainda der erro de project-id:

1. **Verificar arquivo de credenciais**:
   ```bash
   ls -la env-gemini/gen-lang-client-**********-e8c38b0d9293.json
   ```

2. **Verificar gcloud configurado**:
   ```bash
   gcloud config get-value project
   gcloud auth application-default print-access-token
   ```

3. **Verificar variáveis de ambiente**:
   ```bash
   echo $GOOGLE_APPLICATION_CREDENTIALS
   echo $GOOGLE_CLOUD_PROJECT
   ```

### Se der erro de permissões:

1. **Habilitar APIs necessárias**:
   ```bash
   gcloud services enable aiplatform.googleapis.com
   gcloud services enable vertexai.googleapis.com
   ```

2. **Verificar permissões do service account**:
   - Vertex AI User
   - AI Platform Developer

## Dependências Adicionadas

```xml
<!-- Google Cloud Vertex AI -->
<dependency>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-vertexai</artifactId>
    <version>0.7.0</version>
</dependency>
```

## Versões Utilizadas

- Spring AI: 1.0.0-M4
- Google Cloud Vertex AI: 0.7.0
- Modelo: gemini-1.5-flash

## Próximos Passos

1. Iniciar a aplicação
2. Verificar os logs de configuração
3. Testar o endpoint do assistente
4. Monitorar logs para possíveis erros adicionais
