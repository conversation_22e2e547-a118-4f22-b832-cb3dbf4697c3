# Exemplos de Uso do Assistente de IA

Este documento contém exemplos práticos de como interagir com o assistente de IA do sistema de estudos organizados.

## Exemplos de Interações

### 1. Saudação e Apresentação

**Usuário:**
```json
{
  "mensagem": "Ol<PERSON>, qual o seu nome?"
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Olá! Eu sou o assistente de IA do sistema de estudos organizados. Posso ajudá-lo a cadastrar disciplinas, criar planejamentos de estudo e organizar seus estudos para concursos públicos. Como posso ajudá-lo hoje?"
}
```

### 2. Cadastrar Disciplina Simples

**Usuário:**
```json
{
  "mensagem": "Por favor, cadastre a disciplina 'Física Quântica'."
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Disciplina 'Física Quântica' foi cadastrada com sucesso com o ID 15."
}
```

### 3. Cadastrar Múltiplas Disciplinas

**Usuário:**
```json
{
  "mensagem": "Quero cadastrar as disciplinas Cálculo 2 e Álgebra Linear."
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Disciplina 'Cálculo 2' foi cadastrada com sucesso com o ID 16.\nDisciplina 'Álgebra Linear' foi cadastrada com sucesso com o ID 17."
}
```

### 4. Criar Planejamento Simples

**Usuário:**
```json
{
  "mensagem": "quero planejar meus estudos. Tenho 20 horas na semana para estudar Cálculo 2, Física Quântica e IA Generativa"
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Planejamento de estudos 'Plano de Estudos - Concurso' foi gerado com IA e salvo com sucesso. O plano inclui 20 horas semanais distribuídas entre as disciplinas especificadas."
}
```

### 5. Criar Planejamento Detalhado

**Usuário:**
```json
{
  "mensagem": "Crie um planejamento de estudos para concurso público federal. Tenho 25 horas semanais disponíveis. Quero estudar: Português (nível 4, peso 5), Matemática (nível 2, peso 4), Direito Administrativo (nível 3, peso 5), Direito Constitucional (nível 3, peso 4) e Informática (nível 5, peso 2)."
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Planejamento de estudos 'Preparação Concurso Federal' foi gerado com IA e salvo com sucesso. O plano inclui 25 horas semanais distribuídas entre as disciplinas especificadas."
}
```

### 6. Pergunta sobre Funcionalidades

**Usuário:**
```json
{
  "mensagem": "Quero criar uma nova disciplina."
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Claro! Para cadastrar uma nova disciplina, preciso saber o nome dela. Qual disciplina você gostaria de cadastrar?"
}
```

### 7. Solicitação de Ajuda

**Usuário:**
```json
{
  "mensagem": "Como você pode me ajudar?"
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Posso ajudá-lo com as seguintes funcionalidades:\n\n1. **Cadastrar Disciplinas**: Posso cadastrar novas disciplinas no sistema\n2. **Criar Planejamentos**: Posso criar planejamentos de estudo personalizados\n3. **Planejamento com IA**: Posso gerar planejamentos inteligentes baseados em suas necessidades\n\nPor exemplo:\n- \"Cadastre a disciplina Matemática\"\n- \"Crie um planejamento de 20 horas semanais para Português e Matemática\"\n- \"Quero um planejamento para concurso com as disciplinas X, Y e Z\"\n\nComo posso ajudá-lo hoje?"
}
```

### 8. Tratamento de Erro

**Usuário:**
```json
{
  "mensagem": "Cadastre a disciplina"
}
```

**Resposta Esperada:**
```json
{
  "resposta": "Para cadastrar uma disciplina, preciso saber o nome dela. Poderia me informar qual disciplina você gostaria de cadastrar?"
}
```

## Casos de Teste para Desenvolvimento

### Teste 1: Function Calling - Cadastrar Disciplina
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Cadastre a disciplina Estatística"}'
```

### Teste 2: Function Calling - Planejamento IA
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Crie um planejamento de estudos para concurso. Tenho 15 horas semanais. Disciplinas: Português (nível 3, peso 5), Matemática (nível 2, peso 4)"}'
```

### Teste 3: Conversação Sem Function Calling
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Qual a melhor estratégia para estudar para concursos?"}'
```

## Notas para Desenvolvimento

1. **Autenticação**: Todos os endpoints requerem JWT token válido
2. **Function Calling**: O assistente automaticamente decide quando usar as funções disponíveis
3. **Tratamento de Erros**: Erros são capturados e retornados como mensagens amigáveis
4. **Logs**: Todas as interações são logadas para debug e monitoramento

## Validação da Implementação

Para validar se a implementação está funcionando corretamente:

1. **Teste de Conectividade**: Verifique se o Gemini responde a mensagens simples
2. **Teste de Function Calling**: Confirme se as funções são chamadas corretamente
3. **Teste de Autenticação**: Verifique se apenas usuários autenticados podem acessar
4. **Teste de Erro**: Confirme se erros são tratados adequadamente
