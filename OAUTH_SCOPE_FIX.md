# Correção do Erro "invalid_scope" - OAuth 2

## Erro Identificado
```
<PERSON>rro<PERSON> getting access token for service account: 400 Bad Request
{"error":"invalid_scope","error_description":"Invalid OAuth scope or ID token audience provided."}
```

## Causa do Problema
O service account `<EMAIL>` não tem permissão para usar os escopos OAuth solicitados.

## Soluções Implementadas

### Solução 1: Configuração Simples (Recomendada)
Criamos uma configuração que usa as credenciais sem modificar escopos:

- ✅ Perfil `simple` ativado
- ✅ Sem modificação de escopos OAuth
- ✅ Usa permissões nativas do service account

### Solução 2: Verificar Permissões do Service Account

#### No Google Cloud Console:
1. Ir para **IAM & Admin > IAM**
2. Encontrar o service account: `<EMAIL>`
3. Verificar se tem as seguintes permissões:
   - **Vertex AI User**
   - **AI Platform Developer** 
   - **Service Account Token Creator**

#### Adicionar Permissões (se necessário):
```bash
# Vertex AI User
gcloud projects add-iam-policy-binding gen-lang-client-********** \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

# AI Platform Developer
gcloud projects add-iam-policy-binding gen-lang-client-********** \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/ml.developer"
```

### Solução 3: Habilitar APIs Necessárias
```bash
# Habilitar Vertex AI API
gcloud services enable aiplatform.googleapis.com --project=gen-lang-client-**********

# Habilitar Generative AI API
gcloud services enable generativelanguage.googleapis.com --project=gen-lang-client-**********
```

## Como Testar

### 1. Compilar e Executar
```bash
./mvnw clean compile
./mvnw spring-boot:run
```

### 2. Verificar Logs
Procure por:
```
=== CONFIGURAÇÃO SIMPLES DO VERTEX AI ===
Carregando credenciais do arquivo: /path/to/credentials.json
Service Account: <EMAIL>
VertexAI configurado com sucesso!
```

### 3. Testar Health Check
```bash
curl http://localhost:8080/v1/vertex-ai/health
```

### 4. Testar Comunicação
```bash
curl http://localhost:8080/v1/vertex-ai/test-simple
```

## Alternativas se Ainda Não Funcionar

### Opção A: Recriar Service Account
1. **Deletar service account atual**:
   ```bash
   gcloud iam service-<NAME_EMAIL>
   ```

2. **Criar novo service account**:
   ```bash
   gcloud iam service-accounts create vertex-ai-service \
       --display-name="Vertex AI Service Account"
   ```

3. **Adicionar permissões**:
   ```bash
   gcloud projects add-iam-policy-binding gen-lang-client-********** \
       --member="serviceAccount:<EMAIL>" \
       --role="roles/aiplatform.user"
   ```

4. **Gerar nova chave**:
   ```bash
   gcloud iam service-accounts keys create credentials.json \
       --iam-account=<EMAIL>
   ```

### Opção B: Usar Credenciais do Usuário
```bash
# Fazer login como usuário
gcloud auth application-default login

# Definir projeto
gcloud config set project gen-lang-client-**********

# Remover variável de ambiente
unset GOOGLE_APPLICATION_CREDENTIALS
```

### Opção C: Verificar Quotas e Limites
1. Ir para **Google Cloud Console > Quotas**
2. Filtrar por "Vertex AI"
3. Verificar se há quotas disponíveis

## Configuração Atual

Com o perfil `simple` ativo:
- ✅ Sem modificação de escopos OAuth
- ✅ Usa credenciais nativas do service account
- ✅ Configuração mais robusta e compatível
- ✅ Logs detalhados para debug

## Próximos Passos

1. **Executar aplicação** com perfil `simple`
2. **Verificar logs** de inicialização
3. **Testar endpoints** de diagnóstico
4. **Se ainda falhar**: Verificar permissões do service account
5. **Última opção**: Recriar service account com permissões corretas

O perfil `simple` deve resolver o problema de escopo OAuth na maioria dos casos.
