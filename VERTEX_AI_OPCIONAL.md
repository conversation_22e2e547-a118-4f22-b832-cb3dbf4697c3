# Configuração Opcional do Vertex AI

## ✅ Problema Resolvido

O projeto agora **sobe normalmente** mesmo sem as configurações do Vertex AI. As funcionalidades de IA são **completamente opcionais**.

## Como Funciona

### 🔧 Configuração Condicional
Todas as classes relacionadas ao Vertex AI usam `@ConditionalOnProperty(name = "spring.ai.vertex.ai.project-id")`:

- ✅ **VertexAiConfig** - Configuração do Vertex AI
- ✅ **VertexAiConfigSimple** - Configuração simplificada
- ✅ **ChatClientConfig** - Cliente de chat
- ✅ **AiFunctionsConfig** - Funções de IA
- ✅ **AssistantController** - Endpoint do assistente
- ✅ **VertexAiTestController** - Endpoints de teste
- ✅ **VertexAiHealthCheck** - Verificação de saúde

### 📋 Estados do Projeto

#### **Sem Vertex AI (Padrão)**
```properties
# Configurações comentadas no application.properties
#spring.ai.vertex.ai.project-id=gen-lang-client-**********
```

**Resultado:**
- ✅ Projeto sobe normalmente
- ✅ Todas as funcionalidades principais funcionam
- ❌ Assistente de IA não disponível
- ❌ Endpoints `/v1/assistant/*` não existem

#### **Com Vertex AI (Opcional)**
```properties
# Descomente no application.properties
spring.ai.vertex.ai.project-id=gen-lang-client-**********
spring.ai.vertex.ai.location=us-central1
spring.ai.vertex.ai.gemini.model=gemini-1.5-flash
spring.ai.vertex.ai.gemini.temperature=0.7
spring.ai.vertex.ai.gemini.max-output-tokens=2048
```

**Resultado:**
- ✅ Projeto sobe normalmente
- ✅ Todas as funcionalidades principais funcionam
- ✅ Assistente de IA disponível
- ✅ Endpoints `/v1/assistant/*` funcionais

## Como Habilitar o Vertex AI

### 1. Configurar Credenciais
```bash
# Opção A: gcloud CLI
gcloud auth application-default login

# Opção B: Variável de ambiente
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json
```

### 2. Editar application.properties
Descomente as linhas:
```properties
spring.ai.vertex.ai.project-id=gen-lang-client-**********
spring.ai.vertex.ai.location=us-central1
spring.ai.vertex.ai.gemini.model=gemini-1.5-flash
spring.ai.vertex.ai.gemini.temperature=0.7
spring.ai.vertex.ai.gemini.max-output-tokens=2048
```

### 3. Reiniciar Aplicação
```bash
./mvnw spring-boot:run
```

### 4. Testar Assistente
```bash
curl -X POST http://localhost:8080/v1/assistant/chat \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"mensagem": "Olá, como você pode me ajudar?"}'
```

## Como Desabilitar o Vertex AI

### 1. Comentar Configurações
No `application.properties`, comente as linhas:
```properties
#spring.ai.vertex.ai.project-id=gen-lang-client-**********
#spring.ai.vertex.ai.location=us-central1
#spring.ai.vertex.ai.gemini.model=gemini-1.5-flash
#spring.ai.vertex.ai.gemini.temperature=0.7
#spring.ai.vertex.ai.gemini.max-output-tokens=2048
```

### 2. Reiniciar Aplicação
```bash
./mvnw spring-boot:run
```

**Resultado:** Projeto funciona normalmente sem IA.

## Funcionalidades Sempre Disponíveis

Independente do Vertex AI estar habilitado ou não:

- ✅ **Autenticação JWT**
- ✅ **CRUD de Usuários**
- ✅ **CRUD de Disciplinas**
- ✅ **CRUD de Planejamentos**
- ✅ **Planejamento Inteligente** (sem IA)
- ✅ **Swagger/OpenAPI**
- ✅ **Health Check**
- ✅ **Banco de Dados PostgreSQL**

## Funcionalidades Condicionais (Só com Vertex AI)

- ⚡ **Assistente de IA Conversacional**
- ⚡ **Function Calling** (cadastrar disciplina, criar planejamento)
- ⚡ **Planejamento com IA**
- ⚡ **Endpoints de teste do Vertex AI**

## Logs de Inicialização

### Sem Vertex AI
```
Started EstudoOrganizadoMsApplication in 3.2 seconds
```

### Com Vertex AI
```
=== CONFIGURAÇÃO SIMPLES DO VERTEX AI ===
Carregando credenciais do arquivo: /path/to/credentials.json
VertexAI configurado com sucesso!
Started EstudoOrganizadoMsApplication in 4.1 seconds
```

## Vantagens da Configuração Condicional

1. **Flexibilidade**: Projeto funciona com ou sem IA
2. **Desenvolvimento**: Não precisa configurar IA para desenvolver outras funcionalidades
3. **Deploy**: Pode fazer deploy sem credenciais do Google Cloud
4. **Testes**: Testes passam independente da configuração de IA
5. **Manutenção**: Facilita debugging e manutenção

## Resumo

✅ **Projeto sempre funciona** - com ou sem Vertex AI  
✅ **Configuração opcional** - descomente para habilitar  
✅ **Sem dependências obrigatórias** - IA é um extra  
✅ **Logs informativos** - mostra status da configuração  
✅ **Testes passam** - independente da configuração  

**O Vertex AI agora é uma funcionalidade opcional que não impede o funcionamento do projeto!**
