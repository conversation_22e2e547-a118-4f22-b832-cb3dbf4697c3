# Resolução do Erro OAuth 2 - Vertex AI

## Erro Identificado
```
UNAUTHENTICATED: Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential.
```

## Soluções Implementadas

### 1. Configuração Robusta de Credenciais
- ✅ Adicionado escopo `https://www.googleapis.com/auth/cloud-platform`
- ✅ Refresh automático das credenciais
- ✅ Múltiplas formas de carregamento de credenciais
- ✅ Logs detalhados para debug

### 2. Endpoints de Teste Criados
- `/v1/vertex-ai/health` - Verifica configuração e credenciais
- `/v1/vertex-ai/test-simple` - Testa comunicação com Gemini

## Como Testar Agora

### 1. Iniciar a Aplicação
```bash
./mvnw spring-boot:run
```

### 2. Verificar Logs de Inicialização
Procure por:
```
Carregando credenciais do arquivo: /path/to/credentials.json
Credenciais carregadas e validadas com sucesso
=== VERTEX AI CONFIGURADO COM SUCESSO ===
```

### 3. Testar Health Check
```bash
curl http://localhost:8080/v1/vertex-ai/health
```

Resposta esperada:
```json
{
  "projectId": "gen-lang-client-**********",
  "location": "us-central1",
  "vertexAiConfigured": true,
  "credentialsFound": true,
  "accessTokenValid": true,
  "status": "OK"
}
```

### 4. Testar Comunicação Simples
```bash
curl http://localhost:8080/v1/vertex-ai/test-simple
```

Resposta esperada:
```json
{
  "status": "SUCCESS",
  "response": "Olá",
  "message": "Comunicação com Gemini funcionando!"
}
```

## Possíveis Problemas e Soluções

### Problema 1: Arquivo de Credenciais Inválido
**Sintomas**: `credentialsFound: false` no health check

**Soluções**:
1. Verificar se o arquivo existe:
   ```bash
   ls -la env-gemini/gen-lang-client-**********-e8c38b0d9293.json
   ```

2. Verificar se a variável de ambiente está correta no IntelliJ:
   - `GOOGLE_APPLICATION_CREDENTIALS=C:\path\to\env-gemini\gen-lang-client-**********-e8c38b0d9293.json`

3. Baixar novo arquivo de credenciais do Google Cloud Console

### Problema 2: Service Account sem Permissões
**Sintomas**: `accessTokenValid: false` ou erro 403

**Soluções**:
1. Verificar permissões no Google Cloud Console:
   - Vertex AI User
   - AI Platform Developer
   - Service Account Token Creator

2. Habilitar APIs necessárias:
   ```bash
   gcloud services enable aiplatform.googleapis.com
   gcloud services enable vertexai.googleapis.com
   ```

### Problema 3: Token Expirado
**Sintomas**: Funciona inicialmente, depois para de funcionar

**Solução**: A configuração agora faz refresh automático, mas você pode forçar:
```bash
gcloud auth application-default login
```

### Problema 4: Projeto Incorreto
**Sintomas**: `projectId` diferente do esperado

**Solução**: Verificar se o projeto no arquivo de credenciais é o mesmo do `application.properties`

## Configuração Alternativa (Se Ainda Não Funcionar)

### Opção 1: Usar gcloud CLI
```bash
# Fazer login
gcloud auth application-default login

# Definir projeto
gcloud config set project gen-lang-client-**********

# Verificar configuração
gcloud config list
```

### Opção 2: Variáveis de Ambiente Adicionais
Adicionar no IntelliJ:
```
GOOGLE_CLOUD_PROJECT=gen-lang-client-**********
GOOGLE_APPLICATION_CREDENTIALS=C:\path\to\credentials.json
```

### Opção 3: Recriar Service Account
1. Ir ao Google Cloud Console
2. IAM & Admin > Service Accounts
3. Criar novo service account
4. Adicionar permissões:
   - Vertex AI User
   - AI Platform Developer
5. Gerar nova chave JSON
6. Atualizar arquivo de credenciais

## Verificação Final

Após implementar as soluções:

1. **Health Check deve retornar OK**
2. **Test Simple deve retornar resposta do Gemini**
3. **Logs não devem mostrar erros de autenticação**
4. **Endpoint do assistente deve funcionar**

## Logs Importantes

Procure por estes logs para debug:
```
Carregando credenciais do arquivo: ...
Credenciais carregadas e validadas com sucesso
Criando ChatClient com modelo: ...
=== VERTEX AI CONFIGURADO COM SUCESSO ===
```

Se ainda houver problemas, os endpoints de teste fornecerão informações detalhadas sobre o que está falhando.
