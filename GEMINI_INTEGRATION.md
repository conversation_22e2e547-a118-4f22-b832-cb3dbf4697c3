# Integração com Google Gemini AI

Este documento descreve como configurar e usar a integração com a IA Gemini do Google para geração automática de planejamentos de estudo.

## Configuração

### 1. Obter Chave da API Gemini

1. Acesse o [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Faça login com sua conta Google (preferencialmente a conta PRO para estudantes)
3. Clique em "Create API Key"
4. Copie a chave gerada

### 2. Configurar Variável de Ambiente

Configure a variável de ambiente `GEMINI_API_KEY` com sua chave:

**Windows:**
```bash
set GEMINI_API_KEY=sua-chave-aqui
```

**Linux/Mac:**
```bash
export GEMINI_API_KEY=sua-chave-aqui
```

**Docker/Docker Compose:**
```yaml
environment:
  - GEMINI_API_KEY=sua-chave-aqui
```

### 3. Configurações Opcionais

No arquivo `application.properties`, você pode ajustar:

```properties
# Timeout para requisições (em milissegundos)
gemini.api.timeout=30000

# Máximo de tokens na resposta
gemini.api.max-tokens=2048
```

## Uso da API

### Endpoint: Gerar Planejamento

**POST** `/v1/planejamento-ia/gerar`

Cria um planejamento completo e salva no banco de dados.

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Body:**
```json
{
  "objetivoEstudo": "Concurso Público Federal - Analista de Sistemas",
  "horasDisponiveisPorSemana": 25,
  "minutosDuracaoMaximaPorSessao": 120,
  "disciplinasDesejadas": "Português, Matemática, Informática, Direito Administrativo, Direito Constitucional",
  "nivelConhecimentoGeral": "Intermediário",
  "tempoDisponivel": "6 meses para estudar",
  "preferenciaEstudo": "Prefiro estudar pela manhã e à noite",
  "observacoesAdicionais": "Tenho dificuldade em matemática e preciso focar mais nessa área"
}
```

**Resposta (201 Created):**
```json
{
  "id": 1,
  "nome": "Planejamento Concurso Federal - Analista de Sistemas",
  "dataCriacao": "2024-01-15T10:30:00",
  "horasDisponiveisPorSemana": 25,
  "minutosDuracaoMaximaPorSessao": 120,
  "intervalosRevisao": "1,3,7,14,30,60,120",
  "cicloEstudo": {
    "id": 1,
    "nome": "Ciclo Preparatório - Analista de Sistemas",
    "disciplinas": [
      {
        "ordem": 1,
        "tempoEstudoMeta": "08:00:00",
        "nivelConhecimento": 2,
        "peso": 5,
        "disciplina": {
          "id": 1,
          "nome": "Matemática"
        }
      },
      {
        "ordem": 2,
        "tempoEstudoMeta": "06:00:00",
        "nivelConhecimento": 3,
        "peso": 4,
        "disciplina": {
          "id": 2,
          "nome": "Português"
        }
      }
    ]
  }
}
```

### Endpoint: Obter Sugestão

**POST** `/v1/planejamento-ia/sugestao`

Gera uma sugestão de planejamento sem salvar no banco (para preview).

Mesma estrutura de request e response do endpoint anterior.

## Campos de Entrada

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `objetivoEstudo` | String | Sim | Descrição do objetivo de estudo (ex: "Concurso Público Federal") |
| `horasDisponiveisPorSemana` | Integer | Sim | Horas disponíveis por semana (1-168) |
| `minutosDuracaoMaximaPorSessao` | Integer | Sim | Duração máxima por sessão em minutos (15-480) |
| `disciplinasDesejadas` | String | Não | Lista de disciplinas desejadas |
| `nivelConhecimentoGeral` | String | Não | Nível geral de conhecimento |
| `tempoDisponivel` | String | Não | Tempo disponível para estudar |
| `preferenciaEstudo` | String | Não | Preferências de horário/método |
| `observacoesAdicionais` | String | Não | Observações específicas |

## Códigos de Resposta

| Código | Descrição |
|--------|-----------|
| 201 | Planejamento criado com sucesso |
| 200 | Sugestão gerada com sucesso |
| 400 | Dados de entrada inválidos |
| 401 | Usuário não autenticado |
| 500 | Erro de configuração da IA |
| 503 | Serviço de IA temporariamente indisponível |

## Tratamento de Erros

### Erro de Configuração
```json
{
  "message": "Serviço de IA não configurado corretamente. Contate o administrador.",
  "timestamp": 1642248600000
}
```

### Erro de Comunicação
```json
{
  "message": "Serviço de IA temporariamente indisponível. Tente novamente em alguns minutos.",
  "timestamp": 1642248600000
}
```

### Erro de Validação
```json
{
  "message": "Dados inválidos: A descrição do objetivo de estudo é obrigatória",
  "timestamp": 1642248600000
}
```

## Dicas de Uso

1. **Seja específico no objetivo**: Quanto mais detalhado o objetivo, melhor será o planejamento gerado.

2. **Forneça contexto**: Use os campos opcionais para dar mais contexto sobre seu nível e preferências.

3. **Disciplinas existentes**: O sistema reutiliza disciplinas já cadastradas pelo usuário quando possível.

4. **Tempo realista**: Forneça horas semanais e duração por sessão realistas para sua rotina.

## Limitações

- A qualidade do planejamento depende da qualidade da descrição fornecida
- A API do Gemini tem limites de uso (rate limiting)
- Respostas podem variar entre execuções devido à natureza da IA
- Requer conexão com internet para funcionar

## Troubleshooting

### Erro "Chave da API não configurada"
- Verifique se a variável de ambiente `GEMINI_API_KEY` está configurada
- Certifique-se de que a chave não contém espaços ou caracteres especiais

### Erro "Limite de requisições excedido"
- Aguarde alguns minutos antes de tentar novamente
- Considere implementar retry com backoff exponencial

### Erro "Resposta inválida da IA"
- Tente reformular a descrição do objetivo
- Verifique se todos os campos obrigatórios estão preenchidos
- Tente novamente, pois pode ser um erro temporário da IA
